using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using TMPro;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

public class InventorySlotUI : MonoBehaviour, IInventorySlotUI, IPointerEnterHandler, IPointerExitHandler, IPointerClickHandler
{
    [Header("UI Components")]
    [SerializeField] RectTransform spawnItemParent;
    [SerializeField] GameObject itemModel;
    [SerializeField] public ItemSO itemSO;
    public IInventoryUI parentUI;

    [Tooltip("Image component to display the item's icon.")]
    [SerializeField] private Image itemIcon;

    [Tooltip("Raw Image component to display rendered item texture.")]
    [SerializeField] private RawImage itemRenderImage;

    [Tooltip("Text component to display the item's stack amount.")]
    [SerializeField] private TextMeshProUGUI amountText;

    enum ShowType {Model, Icon, RenderedModel};
    [SerializeField] private ShowType showType = ShowType.RenderedModel;

    // Item rendering system reference
    private ItemRenderRequest currentRenderRequest;
    
    public System.Action<IInventorySlotUI, bool> OnSlotHovered { get; set; }
    public System.Action<IInventorySlotUI> OnSlotPressed { get; set; }

    public IInventoryUI GetParentUI() { return parentUI; }

    public void Awake()
    {
        GridLayoutGroup layoutGroup = GetComponentInParent<GridLayoutGroup>();
        if (layoutGroup != null)
        {
            Debug.Log("layout encontrado");
            layoutGroup.OnLayoutUpdate += UpdateModelPosition;
        }
    }

    /// <summary>
    /// Updates the slot's visual representation based on the slot data.
    /// </summary>
    /// <param name="slotData">The data from the inventory slot to display.</param>
    public void UpdateSlot(IInventorySlot slotData)
    {
        if (slotData != null && !slotData.IsEmpty())
        {
            itemSO = slotData.Item();
            switch (showType)
            {
                case ShowType.Model:
                    itemIcon.enabled = false;
                    if (itemRenderImage != null) itemRenderImage.enabled = false;
                    ShowModel();
                    break;
                case ShowType.Icon:
                    itemIcon.enabled = true;
                    if (itemRenderImage != null) itemRenderImage.enabled = false;
                    ShowIcon();
                    break;
                case ShowType.RenderedModel:
                    itemIcon.enabled = false;
                    if (itemRenderImage != null) itemRenderImage.enabled = true;
                    ShowRenderedModel();
                    break;
            }

            // Mostra a quantidade apenas se o item for empilhável e tiver mais de 1
            if (slotData.Amount() > 1)
            {
                amountText.enabled = true;
                amountText.text = slotData.Amount().ToString();
            }
            else
            {
                amountText.enabled = false;
            }
        }
        else
        {
            // Se o slot estiver vazio, limpa a UI
            Clear();
        }
    }

    public void UpdateModelPosition()
    {
        if (showType == ShowType.Model)
        {
            if(itemModel == null) return;
            Debug.Log("Update position");
            RectTransformUtils.FitGameObject(spawnItemParent, itemModel, itemSO.modelInfo);
        }
    }

    [ContextMenu("Fit")]
    public void FitButton()
    {
        if (showType == ShowType.Model)
        {
            if(itemModel == null) return;
            Debug.Log("Update position");
            RectTransformUtils.FitGameObject(spawnItemParent, itemModel, itemSO.modelInfo);
        }
    }

    public void HoverSlot()
    {
        
    }

    public void ShowIcon()
    {
        // Ativa os componentes e define os valores
        itemIcon.enabled = true;
        itemIcon.sprite = itemSO.icon;
    }

    public void ShowModel()
    {
        itemModel = Instantiate(itemSO.prefab, spawnItemParent);
        Rigidbody rb = itemModel.GetComponentInChildren<Rigidbody>();
        Collider col = itemModel.GetComponentInChildren<Collider>();


        if (rb != null) rb.isKinematic = true;
        if (col != null) col.isTrigger = true;
    }

    public void ShowRenderedModel()
    {
        if (itemSO == null || itemRenderImage == null)
        {
            Debug.LogWarning("Cannot show rendered model: ItemSO or RawImage is null");
            return;
        }

        // Release previous render request if any
        if (currentRenderRequest != null && ItemRenderingSystem.Instance != null)
        {
            ItemRenderingSystem.Instance.ReleaseRenderRequest(currentRenderRequest);
        }

        // Request new render
        if (ItemRenderingSystem.Instance != null)
        {
            currentRenderRequest = ItemRenderingSystem.Instance.RequestItemRender(itemSO);
        }
        else
        {
            Debug.LogError("ItemRenderingSystem not found! Make sure it's in the scene.");
            FallbackToIcon();
            return;
        }

        if (currentRenderRequest != null)
        {
            // Start checking for completion
            StartCoroutine(WaitForRenderCompletion());
        }
        else
        {
            Debug.LogError("Failed to create render request for item: " + itemSO.itemName);
        }
    }

    private System.Collections.IEnumerator WaitForRenderCompletion()
    {
        if (currentRenderRequest == null) yield break;

        // Wait for rendering to complete
        while (currentRenderRequest.IsProcessing())
        {
            yield return null;
        }

        // Check if rendering was successful
        if (currentRenderRequest.IsReady())
        {
            // Apply the rendered texture to the Raw Image
            itemRenderImage.texture = currentRenderRequest.GetTexture();
        }
        else if (currentRenderRequest.HasFailed())
        {
            Debug.LogError("Item rendering failed for: " + itemSO.itemName);
            // Fallback to icon or model
            FallbackToIcon();
        }
    }

    private void FallbackToIcon()
    {
        if (itemIcon != null && itemSO != null && itemSO.icon != null)
        {
            itemIcon.enabled = true;
            itemIcon.sprite = itemSO.icon;
            if (itemRenderImage != null) itemRenderImage.enabled = false;
        }
    }

    public void OnPointerEnter(UnityEngine.EventSystems.PointerEventData pointerEventData)
    {
        Debug.Log("Hovering slot: " + this);    
        OnSlotHovered.Invoke(this, true);
    }

    public void OnPointerExit(UnityEngine.EventSystems.PointerEventData pointerEventData)
    {
        Debug.Log("Hovering slot: 2 " + this);
        OnSlotHovered.Invoke(this, false);
    }

    public void OnPointerClick(UnityEngine.EventSystems.PointerEventData pointerEventData)
    {
        if (OnSlotPressed != null) OnSlotPressed(this);
    }

    /// <summary>
    /// Clears the slot's visual representation, making it appear empty.
    /// </summary>
    public void Clear()
    {
        // Clean up render request
        if (currentRenderRequest != null && ItemRenderingSystem.Instance != null)
        {
            ItemRenderingSystem.Instance.ReleaseRenderRequest(currentRenderRequest);
            currentRenderRequest = null;
        }

        // Clean up 3D model
        if (itemModel != null)
        {
            Destroy(itemModel);
            itemModel = null;
        }

        // Clear UI elements
        itemSO = null;
        itemIcon.enabled = false;
        itemIcon.sprite = null;

        if (itemRenderImage != null)
        {
            itemRenderImage.enabled = false;
            itemRenderImage.texture = null;
        }

        amountText.enabled = false;
        amountText.text = "";
    }

    void OnDestroy()
    {
        // Clean up render request when slot is destroyed
        if (currentRenderRequest != null && ItemRenderingSystem.Instance != null)
        {
            ItemRenderingSystem.Instance.ReleaseRenderRequest(currentRenderRequest);
        }
    }
}