using UnityEngine;
using System.Collections.Generic;
using System.Collections;

/// <summary>
/// Manages isolated rendering of inventory items using dedicated cameras and render textures.
/// Ensures complete separation from main game cameras and provides efficient texture pooling.
/// </summary>
public class ItemRenderingSystem : MonoBehaviour
{
    [Header("Rendering Configuration")]
    [Tooltip("Layer used for item rendering isolation")]
    [SerializeField] private int itemRenderLayer = 30;
    
    [<PERSON><PERSON><PERSON>("Default render texture size for items")]
    [SerializeField] private int defaultTextureSize = 256;
    
    [Tooltip("Position offset for item rendering area (far from players)")]
    [SerializeField] private Vector3 renderAreaOffset = new Vector3(10000, 10000, 10000);
    
    [Tooltip("Maximum number of concurrent render requests")]
    [SerializeField] private int maxConcurrentRenders = 10;

    [<PERSON><PERSON>("Camera Settings")]
    [Tooltip("Camera prefab for item rendering")]
    [SerializeField] private GameObject itemCameraPrefab;
    
    [Tooltip("Default camera distance from items")]
    [SerializeField] private float defaultCameraDistance = 2f;
    
    [Tooltip("Camera field of view")]
    [SerializeField] private float cameraFOV = 30f;

    // Singleton instance
    private static ItemRenderingSystem _instance;
    public static ItemRenderingSystem Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = FindObjectOfType<ItemRenderingSystem>();
                if (_instance == null)
                {
                    GameObject go = new GameObject("ItemRenderingSystem");
                    _instance = go.AddComponent<ItemRenderingSystem>();
                }
            }
            return _instance;
        }
    }

    // Active render requests
    private Dictionary<int, ItemRenderRequest> activeRequests = new Dictionary<int, ItemRenderRequest>();
    private Queue<ItemRenderRequest> pendingRequests = new Queue<ItemRenderRequest>();
    private int nextRequestId = 0;

    // Render texture manager
    private RenderTextureManager textureManager;
    
    // Camera pool
    private Queue<ItemRenderCamera> availableCameras = new Queue<ItemRenderCamera>();
    private List<ItemRenderCamera> allCameras = new List<ItemRenderCamera>();

    void Awake()
    {
        if (_instance == null)
        {
            _instance = this;
            DontDestroyOnLoad(gameObject);
            Initialize();
        }
        else if (_instance != this)
        {
            Destroy(gameObject);
        }
    }

    void Initialize()
    {
        // Initialize render texture manager
        textureManager = gameObject.AddComponent<RenderTextureManager>();
        textureManager.Initialize(defaultTextureSize);

        // Create initial camera pool
        CreateInitialCameraPool();

        // Set up layer isolation
        SetupLayerIsolation();
    }

    void CreateInitialCameraPool()
    {
        for (int i = 0; i < maxConcurrentRenders; i++)
        {
            CreateNewCamera();
        }
    }

    ItemRenderCamera CreateNewCamera()
    {
        GameObject cameraGO;
        
        if (itemCameraPrefab != null)
        {
            cameraGO = Instantiate(itemCameraPrefab, transform);
        }
        else
        {
            cameraGO = new GameObject($"ItemRenderCamera_{allCameras.Count}");
            cameraGO.transform.SetParent(transform);
            cameraGO.AddComponent<Camera>();
        }

        // Position camera in isolated area
        cameraGO.transform.position = renderAreaOffset + Vector3.forward * allCameras.Count * 10f;

        ItemRenderCamera renderCamera = cameraGO.GetComponent<ItemRenderCamera>();
        if (renderCamera == null)
        {
            renderCamera = cameraGO.AddComponent<ItemRenderCamera>();
        }

        renderCamera.Initialize(itemRenderLayer, cameraFOV, defaultCameraDistance);
        
        allCameras.Add(renderCamera);
        availableCameras.Enqueue(renderCamera);

        return renderCamera;
    }

    void SetupLayerIsolation()
    {
        // Set up complete isolation using the layer manager
        ItemRenderingLayerManager.SetupIsolation(itemRenderLayer);

        // Log isolation status
        var isolationInfo = ItemRenderingLayerManager.GetIsolationInfo(itemRenderLayer);
        Debug.Log($"Item rendering isolation setup complete: {isolationInfo}");

        if (!isolationInfo.isFullyIsolated)
        {
            Debug.LogWarning("Not all main cameras are isolated from item render layer. This may cause visual conflicts.");
        }
    }

    /// <summary>
    /// Requests rendering of an item and returns a render request object
    /// </summary>
    public ItemRenderRequest RequestItemRender(ItemSO item, int textureSize = -1)
    {
        if (item == null || item.prefab == null)
        {
            Debug.LogWarning("Cannot render item: item or prefab is null");
            return null;
        }

        int requestId = nextRequestId++;
        int finalTextureSize = textureSize > 0 ? textureSize : defaultTextureSize;

        ItemRenderRequest request = new ItemRenderRequest
        {
            requestId = requestId,
            item = item,
            textureSize = finalTextureSize,
            status = RenderStatus.Pending
        };

        if (activeRequests.Count < maxConcurrentRenders)
        {
            ProcessRenderRequest(request);
        }
        else
        {
            pendingRequests.Enqueue(request);
        }

        return request;
    }

    void ProcessRenderRequest(ItemRenderRequest request)
    {
        if (availableCameras.Count == 0)
        {
            // Create new camera if needed
            CreateNewCamera();
        }

        ItemRenderCamera camera = availableCameras.Dequeue();
        RenderTexture renderTexture = textureManager.GetRenderTexture(request.textureSize);

        if (renderTexture == null)
        {
            Debug.LogError("Failed to get render texture for item rendering");
            request.status = RenderStatus.Failed;
            return;
        }

        request.status = RenderStatus.Rendering;
        request.renderTexture = renderTexture;
        request.assignedCamera = camera;

        activeRequests[request.requestId] = request;

        // Start rendering process
        StartCoroutine(RenderItemCoroutine(request));
    }

    IEnumerator RenderItemCoroutine(ItemRenderRequest request)
    {
        // Instantiate item model in isolated area
        Vector3 itemPosition = renderAreaOffset + Vector3.right * request.requestId * 5f;
        GameObject itemInstance = Instantiate(request.item.prefab, itemPosition, Quaternion.identity);
        
        // Set item to render layer
        LayerUtility.SetLayerInChildren(itemInstance, itemRenderLayer);

        // Configure camera for this item
        request.assignedCamera.SetupForItem(itemInstance, request.item.modelInfo, request.renderTexture);

        // Wait a frame for setup
        yield return null;

        // Render the item
        request.assignedCamera.RenderItem();

        // Mark as complete
        request.status = RenderStatus.Complete;
        request.itemInstance = itemInstance;

        // Process next pending request if any
        if (pendingRequests.Count > 0)
        {
            ItemRenderRequest nextRequest = pendingRequests.Dequeue();
            ProcessRenderRequest(nextRequest);
        }
    }

    /// <summary>
    /// Releases a render request and cleans up resources
    /// </summary>
    public void ReleaseRenderRequest(ItemRenderRequest request)
    {
        if (request == null) return;

        // Clean up item instance
        if (request.itemInstance != null)
        {
            Destroy(request.itemInstance);
        }

        // Return render texture to pool
        if (request.renderTexture != null)
        {
            textureManager.ReturnRenderTexture(request.renderTexture);
        }

        // Return camera to pool
        if (request.assignedCamera != null)
        {
            availableCameras.Enqueue(request.assignedCamera);
        }

        // Remove from active requests
        activeRequests.Remove(request.requestId);

        request.status = RenderStatus.Released;
    }

    void OnDestroy()
    {
        // Clean up all active requests
        foreach (var request in activeRequests.Values)
        {
            ReleaseRenderRequest(request);
        }

        // Clean up pending requests
        while (pendingRequests.Count > 0)
        {
            var request = pendingRequests.Dequeue();
            ReleaseRenderRequest(request);
        }

        // Restore camera isolation if this was the main instance
        if (_instance == this)
        {
            ItemRenderingLayerManager.RestoreAllCameras();
        }
    }

    void Update()
    {
        // Process any failed requests or cleanup
        ProcessCleanup();
    }

    void ProcessCleanup()
    {
        // This will be expanded for more sophisticated cleanup logic
        // For now, just ensure we don't have stale requests
    }
}
