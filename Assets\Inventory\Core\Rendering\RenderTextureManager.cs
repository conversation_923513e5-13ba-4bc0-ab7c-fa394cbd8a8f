using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Manages render texture creation, pooling, and cleanup for efficient memory management
/// </summary>
public class RenderTextureManager : MonoBehaviour
{
    [Header("Texture Pool Configuration")]
    [SerializeField] private int defaultTextureSize = 256;
    [SerializeField] private int maxPoolSize = 20;
    [SerializeField] private RenderTextureFormat textureFormat = RenderTextureFormat.ARGB32;
    [SerializeField] private int depthBuffer = 24;
    [SerializeField] private bool enableMipmaps = false;

    // Texture pools organized by size
    private Dictionary<int, Queue<RenderTexture>> texturePools = new Dictionary<int, Queue<RenderTexture>>();
    private Dictionary<int, List<RenderTexture>> activeTextures = new Dictionary<int, List<RenderTexture>>();
    private Dictionary<RenderTexture, int> textureToSize = new Dictionary<RenderTexture, int>();

    // Statistics
    private int totalTexturesCreated = 0;
    private int totalTexturesInUse = 0;
    private int totalTexturesPooled = 0;

    /// <summary>
    /// Initializes the render texture manager
    /// </summary>
    public void Initialize(int defaultSize)
    {
        defaultTextureSize = defaultSize;
        
        // Pre-create some textures for common sizes
        PreCreateTextures();
    }

    void PreCreateTextures()
    {
        // Create initial pool for default size
        CreateTexturePool(defaultTextureSize, 5);
        
        // Create pools for common sizes
        CreateTexturePool(128, 3);
        CreateTexturePool(512, 2);
    }

    void CreateTexturePool(int size, int count)
    {
        if (!texturePools.ContainsKey(size))
        {
            texturePools[size] = new Queue<RenderTexture>();
            activeTextures[size] = new List<RenderTexture>();
        }

        for (int i = 0; i < count; i++)
        {
            RenderTexture texture = CreateRenderTexture(size);
            texturePools[size].Enqueue(texture);
            totalTexturesPooled++;
        }
    }

    /// <summary>
    /// Gets a render texture of the specified size from the pool or creates a new one
    /// </summary>
    public RenderTexture GetRenderTexture(int size)
    {
        // Ensure size is valid
        if (size <= 0)
        {
            size = defaultTextureSize;
        }

        // Make sure we have a pool for this size
        if (!texturePools.ContainsKey(size))
        {
            texturePools[size] = new Queue<RenderTexture>();
            activeTextures[size] = new List<RenderTexture>();
        }

        RenderTexture texture;

        // Try to get from pool
        if (texturePools[size].Count > 0)
        {
            texture = texturePools[size].Dequeue();
            totalTexturesPooled--;
        }
        else
        {
            // Create new texture
            texture = CreateRenderTexture(size);
        }

        // Track as active
        activeTextures[size].Add(texture);
        textureToSize[texture] = size;
        totalTexturesInUse++;

        return texture;
    }

    /// <summary>
    /// Returns a render texture to the pool for reuse
    /// </summary>
    public void ReturnRenderTexture(RenderTexture texture)
    {
        if (texture == null) return;

        // Find the size of this texture
        if (!textureToSize.TryGetValue(texture, out int size))
        {
            Debug.LogWarning("Trying to return unknown render texture to pool");
            return;
        }

        // Remove from active list
        if (activeTextures.ContainsKey(size))
        {
            activeTextures[size].Remove(texture);
        }

        totalTexturesInUse--;

        // Check if pool is full
        if (texturePools[size].Count >= maxPoolSize)
        {
            // Pool is full, destroy the texture
            DestroyRenderTexture(texture);
        }
        else
        {
            // Clear the texture and return to pool
            ClearRenderTexture(texture);
            texturePools[size].Enqueue(texture);
            totalTexturesPooled++;
        }
    }

    RenderTexture CreateRenderTexture(int size)
    {
        RenderTexture texture = new RenderTexture(size, size, depthBuffer, textureFormat);
        texture.name = $"ItemRenderTexture_{size}x{size}_{totalTexturesCreated}";
        texture.useMipMap = enableMipmaps;
        texture.autoGenerateMips = enableMipmaps;
        texture.filterMode = FilterMode.Bilinear;
        texture.wrapMode = TextureWrapMode.Clamp;
        
        // Create the texture
        texture.Create();
        
        totalTexturesCreated++;
        
        return texture;
    }

    void ClearRenderTexture(RenderTexture texture)
    {
        if (texture == null) return;

        // Clear the texture content
        RenderTexture previousActive = RenderTexture.active;
        RenderTexture.active = texture;
        GL.Clear(true, true, Color.clear);
        RenderTexture.active = previousActive;
    }

    void DestroyRenderTexture(RenderTexture texture)
    {
        if (texture == null) return;

        textureToSize.Remove(texture);
        
        if (texture.IsCreated())
        {
            texture.Release();
        }
        
        DestroyImmediate(texture);
    }

    /// <summary>
    /// Cleans up unused textures from pools
    /// </summary>
    public void CleanupPools()
    {
        foreach (var pool in texturePools.Values)
        {
            while (pool.Count > 0)
            {
                RenderTexture texture = pool.Dequeue();
                DestroyRenderTexture(texture);
                totalTexturesPooled--;
            }
        }
    }

    /// <summary>
    /// Forces cleanup of all textures (active and pooled)
    /// </summary>
    public void ForceCleanupAll()
    {
        // Clean up pooled textures
        CleanupPools();

        // Clean up active textures
        foreach (var activeList in activeTextures.Values)
        {
            for (int i = activeList.Count - 1; i >= 0; i--)
            {
                DestroyRenderTexture(activeList[i]);
                totalTexturesInUse--;
            }
            activeList.Clear();
        }

        textureToSize.Clear();
    }

    /// <summary>
    /// Gets statistics about texture usage
    /// </summary>
    public RenderTextureStats GetStats()
    {
        return new RenderTextureStats
        {
            totalCreated = totalTexturesCreated,
            totalInUse = totalTexturesInUse,
            totalPooled = totalTexturesPooled,
            poolCount = texturePools.Count
        };
    }

    /// <summary>
    /// Logs current texture usage statistics
    /// </summary>
    public void LogStats()
    {
        var stats = GetStats();
        Debug.Log($"RenderTexture Stats - Created: {stats.totalCreated}, In Use: {stats.totalInUse}, Pooled: {stats.totalPooled}, Pools: {stats.poolCount}");
    }

    void OnDestroy()
    {
        ForceCleanupAll();
    }

    void OnApplicationPause(bool pauseStatus)
    {
        if (pauseStatus)
        {
            // Clean up some textures when app is paused to free memory
            CleanupPools();
        }
    }

#if UNITY_EDITOR
    void OnValidate()
    {
        // Ensure valid values in editor
        maxPoolSize = Mathf.Max(1, maxPoolSize);
        defaultTextureSize = Mathf.Max(64, defaultTextureSize);
    }
#endif
}

/// <summary>
/// Statistics about render texture usage
/// </summary>
[System.Serializable]
public struct RenderTextureStats
{
    public int totalCreated;
    public int totalInUse;
    public int totalPooled;
    public int poolCount;
}
