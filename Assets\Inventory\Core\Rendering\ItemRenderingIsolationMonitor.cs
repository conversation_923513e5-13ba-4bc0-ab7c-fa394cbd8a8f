using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Monitors for new cameras and automatically isolates them from the item render layer
/// </summary>
public class ItemRenderingIsolationMonitor : MonoBehaviour
{
    [Header("Monitoring Configuration")]
    [SerializeField] private int itemRenderLayer = 30;
    [SerializeField] private float checkInterval = 1f;
    [SerializeField] private bool enableDebugLogging = false;
    
    // Track known cameras to detect new ones
    private HashSet<Camera> knownCameras = new HashSet<Camera>();
    private float lastCheckTime;
    
    /// <summary>
    /// Initializes the monitor with the specified item render layer
    /// </summary>
    public void Initialize(int layer)
    {
        itemRenderLayer = layer;
        
        // Initialize with current cameras
        Camera[] existingCameras = FindObjectsOfType<Camera>();
        foreach (Camera cam in existingCameras)
        {
            knownCameras.Add(cam);
        }
        
        lastCheckTime = Time.time;
        
        if (enableDebugLogging)
        {
            Debug.Log($"ItemRenderingIsolationMonitor initialized for layer {itemRenderLayer} with {knownCameras.Count} existing cameras");
        }
    }
    
    void Update()
    {
        // Check for new cameras at specified intervals
        if (Time.time - lastCheckTime >= checkInterval)
        {
            CheckForNewCameras();
            lastCheckTime = Time.time;
        }
    }
    
    void CheckForNewCameras()
    {
        Camera[] allCameras = FindObjectsOfType<Camera>();
        bool foundNewCameras = false;
        
        foreach (Camera cam in allCameras)
        {
            if (!knownCameras.Contains(cam))
            {
                // New camera detected
                knownCameras.Add(cam);
                
                // Isolate it if it's not an item render camera
                if (cam.GetComponent<ItemRenderCamera>() == null)
                {
                    ItemRenderingLayerManager.IsolateCamera(cam, itemRenderLayer);
                    foundNewCameras = true;
                    
                    if (enableDebugLogging)
                    {
                        Debug.Log($"New camera detected and isolated: {cam.name}");
                    }
                }
            }
        }
        
        // Clean up destroyed cameras from our tracking
        CleanupDestroyedCameras();
        
        if (foundNewCameras && enableDebugLogging)
        {
            var isolationInfo = ItemRenderingLayerManager.GetIsolationInfo(itemRenderLayer);
            Debug.Log($"Isolation status: {isolationInfo}");
        }
    }
    
    void CleanupDestroyedCameras()
    {
        // Remove null references from our known cameras set
        knownCameras.RemoveWhere(cam => cam == null);
    }
    
    /// <summary>
    /// Forces an immediate check for new cameras
    /// </summary>
    public void ForceCheck()
    {
        CheckForNewCameras();
    }
    
    /// <summary>
    /// Gets the current isolation status
    /// </summary>
    public LayerIsolationInfo GetIsolationStatus()
    {
        return ItemRenderingLayerManager.GetIsolationInfo(itemRenderLayer);
    }
    
    /// <summary>
    /// Enables or disables debug logging
    /// </summary>
    public void SetDebugLogging(bool enabled)
    {
        enableDebugLogging = enabled;
    }
    
    void OnDestroy()
    {
        if (enableDebugLogging)
        {
            Debug.Log("ItemRenderingIsolationMonitor destroyed");
        }
    }
    
#if UNITY_EDITOR
    void OnValidate()
    {
        // Ensure valid values in editor
        checkInterval = Mathf.Max(0.1f, checkInterval);
        itemRenderLayer = Mathf.Clamp(itemRenderLayer, 0, 31);
    }
    
    void OnDrawGizmosSelected()
    {
        // Draw gizmos to show isolation status
        if (Application.isPlaying)
        {
            var info = GetIsolationStatus();
            
            // Draw a sphere to indicate isolation status
            Gizmos.color = info.isFullyIsolated ? Color.green : Color.red;
            Gizmos.DrawWireSphere(transform.position, 1f);
            
            // Draw text info
            UnityEditor.Handles.Label(transform.position + Vector3.up * 2f, 
                $"Layer {itemRenderLayer}\nIsolated: {info.isolatedMainCameras}/{info.totalMainCameras}");
        }
    }
#endif
}
