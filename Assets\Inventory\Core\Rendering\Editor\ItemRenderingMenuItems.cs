using UnityEngine;
using UnityEditor;

/// <summary>
/// Menu items for the item rendering system
/// </summary>
public static class ItemRenderingMenuItems
{
    [MenuItem("Tools/Item Rendering/Setup Item Rendering System")]
    public static void SetupItemRenderingSystem()
    {
        // Find or create the ItemRenderingSystem
        ItemRenderingSystem existingSystem = Object.FindObjectOfType<ItemRenderingSystem>();
        
        if (existingSystem != null)
        {
            Debug.Log("ItemRenderingSystem already exists in the scene");
            Selection.activeGameObject = existingSystem.gameObject;
            return;
        }

        // Create new system
        GameObject systemGO = new GameObject("ItemRenderingSystem");
        ItemRenderingSystem system = systemGO.AddComponent<ItemRenderingSystem>();
        
        // Configure with sensible defaults
        // Note: These would be set via SerializedObject in a real implementation
        Debug.Log("ItemRenderingSystem created and configured with default settings");
        
        // Select the new system
        Selection.activeGameObject = systemGO;
        
        // Mark scene as dirty
        EditorUtility.SetDirty(systemGO);
        
        Debug.Log("ItemRenderingSystem setup complete! Check the Inspector for configuration options.");
    }

    [MenuItem("Tools/Item Rendering/Create Item Render Camera")]
    public static void CreateItemRenderCamera()
    {
        GameObject cameraGO = new GameObject("ItemRenderCamera");
        Camera cam = cameraGO.AddComponent<Camera>();
        ItemRenderCamera itemCam = cameraGO.AddComponent<ItemRenderCamera>();
        
        // Position away from origin
        cameraGO.transform.position = new Vector3(10000, 10000, 10000);
        
        // Configure camera
        cam.enabled = false;
        cam.clearFlags = CameraClearFlags.SolidColor;
        cam.backgroundColor = Color.clear;
        cam.cullingMask = 1 << 30; // Default item render layer
        cam.depth = -100;
        
        Selection.activeGameObject = cameraGO;
        EditorUtility.SetDirty(cameraGO);
        
        Debug.Log("Item Render Camera created. Configure it in the Inspector.");
    }

    [MenuItem("Tools/Item Rendering/Debug Window")]
    public static void OpenDebugWindow()
    {
        ItemRenderingDebugWindow.ShowWindow();
    }

    [MenuItem("Tools/Item Rendering/Check Layer Isolation")]
    public static void CheckLayerIsolation()
    {
        if (!Application.isPlaying)
        {
            Debug.Log("Layer isolation check requires Play Mode");
            return;
        }

        var isolationInfo = ItemRenderingLayerManager.GetIsolationInfo();
        Debug.Log($"Layer Isolation Status: {isolationInfo}");
        
        if (!isolationInfo.isFullyIsolated)
        {
            Debug.LogWarning("Not all main cameras are isolated from the item render layer!");
            
            // List non-isolated cameras
            Camera[] allCameras = Object.FindObjectsOfType<Camera>();
            int layerMask = 1 << isolationInfo.itemRenderLayer;
            
            foreach (Camera cam in allCameras)
            {
                if (cam.GetComponent<ItemRenderCamera>() != null) continue;
                
                if ((cam.cullingMask & layerMask) != 0)
                {
                    Debug.LogWarning($"Camera '{cam.name}' is not isolated from item render layer", cam);
                }
            }
        }
        else
        {
            Debug.Log("✓ All cameras are properly isolated!");
        }
    }

    [MenuItem("Tools/Item Rendering/Force Setup Isolation")]
    public static void ForceSetupIsolation()
    {
        if (!Application.isPlaying)
        {
            Debug.Log("Isolation setup requires Play Mode");
            return;
        }

        ItemRenderingLayerManager.SetupIsolation();
        var isolationInfo = ItemRenderingLayerManager.GetIsolationInfo();
        Debug.Log($"Isolation setup complete: {isolationInfo}");
    }

    [MenuItem("Tools/Item Rendering/Restore Camera Isolation")]
    public static void RestoreCameraIsolation()
    {
        if (!Application.isPlaying)
        {
            Debug.Log("Camera restoration requires Play Mode");
            return;
        }

        ItemRenderingLayerManager.RestoreAllCameras();
        Debug.Log("All cameras restored to original culling masks");
    }

    [MenuItem("Tools/Item Rendering/Documentation")]
    public static void OpenDocumentation()
    {
        string docPath = "Assets/Inventory/Core/Rendering/README.md";
        
        if (System.IO.File.Exists(docPath))
        {
            Application.OpenURL("file://" + System.IO.Path.GetFullPath(docPath));
        }
        else
        {
            Debug.Log("Documentation not found. Creating basic documentation...");
            CreateDocumentation();
        }
    }

    static void CreateDocumentation()
    {
        string docContent = @"# Item Rendering System

## Overview
The Item Rendering System provides isolated rendering of inventory items using dedicated cameras and render textures.

## Key Features
- Complete isolation from main game cameras
- Efficient render texture pooling
- Automatic layer management
- Debug tools and monitoring

## Setup
1. Use Tools > Item Rendering > Setup Item Rendering System
2. Configure the system in the Inspector
3. Add Raw Image components to your inventory slot UI prefabs
4. Set ShowType to RenderedModel in InventorySlotUI

## Debugging
- Use Tools > Item Rendering > Debug Window for monitoring
- Check layer isolation with Tools > Item Rendering > Check Layer Isolation
- View camera information in the debug window

## Configuration
- Item Render Layer: Layer used for isolation (default: 30)
- Render Area Offset: Position offset for rendering area
- Texture Size: Default render texture size
- Camera Settings: FOV, distance, lighting configuration

## Troubleshooting
- Ensure the item render layer is not used by main game objects
- Check that main cameras are properly isolated
- Verify render textures are being pooled correctly
- Use the debug window to monitor system status
";

        string docPath = "Assets/Inventory/Core/Rendering/README.md";
        System.IO.File.WriteAllText(docPath, docContent);
        AssetDatabase.Refresh();
        
        Debug.Log($"Documentation created at {docPath}");
    }

    // Validation methods for menu items
    [MenuItem("Tools/Item Rendering/Check Layer Isolation", true)]
    public static bool ValidateCheckLayerIsolation()
    {
        return Application.isPlaying;
    }

    [MenuItem("Tools/Item Rendering/Force Setup Isolation", true)]
    public static bool ValidateForceSetupIsolation()
    {
        return Application.isPlaying;
    }

    [MenuItem("Tools/Item Rendering/Restore Camera Isolation", true)]
    public static bool ValidateRestoreCameraIsolation()
    {
        return Application.isPlaying;
    }
}
