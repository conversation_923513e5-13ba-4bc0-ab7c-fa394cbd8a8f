using UnityEngine;
using UnityEditor;

/// <summary>
/// Custom editor for ItemRenderingSystem with debugging and configuration tools
/// </summary>
[CustomEditor(typeof(ItemRenderingSystem))]
public class ItemRenderingSystemEditor : Editor
{
    private ItemRenderingSystem system;
    private bool showDebugInfo = true;
    private bool showCameraInfo = false;
    private bool showTextureInfo = false;

    void OnEnable()
    {
        system = (ItemRenderingSystem)target;
    }

    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Item Rendering System Tools", EditorStyles.boldLabel);

        // System Status
        DrawSystemStatus();

        EditorGUILayout.Space();

        // Debug Information
        showDebugInfo = EditorGUILayout.Foldout(showDebugInfo, "Debug Information");
        if (showDebugInfo)
        {
            DrawDebugInformation();
        }

        // Camera Information
        showCameraInfo = EditorGUILayout.Foldout(showCameraInfo, "Camera Information");
        if (showCameraInfo)
        {
            DrawCameraInformation();
        }

        // Texture Information
        showTextureInfo = EditorGUILayout.Foldout(showTextureInfo, "Texture Information");
        if (showTextureInfo)
        {
            DrawTextureInformation();
        }

        EditorGUILayout.Space();

        // Action Buttons
        DrawActionButtons();
    }

    void DrawSystemStatus()
    {
        EditorGUILayout.BeginVertical(EditorStyles.helpBox);
        EditorGUILayout.LabelField("System Status", EditorStyles.boldLabel);

        if (Application.isPlaying)
        {
            EditorGUILayout.LabelField("Status", "Running");
            
            // Get isolation info
            var isolationInfo = ItemRenderingLayerManager.GetIsolationInfo();
            EditorGUILayout.LabelField("Isolation Status", isolationInfo.isFullyIsolated ? "✓ Fully Isolated" : "⚠ Partial Isolation");
            EditorGUILayout.LabelField("Item Render Layer", isolationInfo.itemRenderLayer.ToString());
            EditorGUILayout.LabelField("Main Cameras Isolated", $"{isolationInfo.isolatedMainCameras}/{isolationInfo.totalMainCameras}");
            EditorGUILayout.LabelField("Item Render Cameras", isolationInfo.itemRenderCameras.ToString());
        }
        else
        {
            EditorGUILayout.LabelField("Status", "Not Running (Play Mode Required)");
        }

        EditorGUILayout.EndVertical();
    }

    void DrawDebugInformation()
    {
        EditorGUILayout.BeginVertical(EditorStyles.helpBox);

        if (Application.isPlaying && system != null)
        {
            // Active requests info would go here
            EditorGUILayout.LabelField("Active Requests", "N/A (Private field)");
            EditorGUILayout.LabelField("Pending Requests", "N/A (Private field)");
            
            // Instance info
            EditorGUILayout.LabelField("Singleton Instance", ItemRenderingSystem.Instance != null ? "✓ Available" : "✗ Not Available");
        }
        else
        {
            EditorGUILayout.LabelField("Debug info available in Play Mode only");
        }

        EditorGUILayout.EndVertical();
    }

    void DrawCameraInformation()
    {
        EditorGUILayout.BeginVertical(EditorStyles.helpBox);

        Camera[] allCameras = FindObjectsOfType<Camera>();
        ItemRenderCamera[] itemCameras = FindObjectsOfType<ItemRenderCamera>();

        EditorGUILayout.LabelField($"Total Cameras: {allCameras.Length}");
        EditorGUILayout.LabelField($"Item Render Cameras: {itemCameras.Length}");
        EditorGUILayout.LabelField($"Main Game Cameras: {allCameras.Length - itemCameras.Length}");

        EditorGUILayout.Space();

        // List item render cameras
        if (itemCameras.Length > 0)
        {
            EditorGUILayout.LabelField("Item Render Cameras:", EditorStyles.boldLabel);
            foreach (var itemCam in itemCameras)
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField($"  • {itemCam.name}");
                if (GUILayout.Button("Select", GUILayout.Width(60)))
                {
                    Selection.activeGameObject = itemCam.gameObject;
                }
                EditorGUILayout.EndHorizontal();
            }
        }

        EditorGUILayout.EndVertical();
    }

    void DrawTextureInformation()
    {
        EditorGUILayout.BeginVertical(EditorStyles.helpBox);

        if (Application.isPlaying && system != null)
        {
            // Try to get texture manager info
            RenderTextureManager textureManager = system.GetComponent<RenderTextureManager>();
            if (textureManager != null)
            {
                var stats = textureManager.GetStats();
                EditorGUILayout.LabelField("Total Textures Created", stats.totalCreated.ToString());
                EditorGUILayout.LabelField("Textures In Use", stats.totalInUse.ToString());
                EditorGUILayout.LabelField("Textures Pooled", stats.totalPooled.ToString());
                EditorGUILayout.LabelField("Pool Count", stats.poolCount.ToString());
            }
            else
            {
                EditorGUILayout.LabelField("RenderTextureManager not found");
            }
        }
        else
        {
            EditorGUILayout.LabelField("Texture info available in Play Mode only");
        }

        EditorGUILayout.EndVertical();
    }

    void DrawActionButtons()
    {
        EditorGUILayout.BeginVertical(EditorStyles.helpBox);
        EditorGUILayout.LabelField("Actions", EditorStyles.boldLabel);

        EditorGUILayout.BeginHorizontal();

        if (GUILayout.Button("Force Isolation Check"))
        {
            if (Application.isPlaying)
            {
                ItemRenderingLayerManager.SetupIsolation();
                var info = ItemRenderingLayerManager.GetIsolationInfo();
                Debug.Log($"Isolation check complete: {info}");
            }
            else
            {
                Debug.Log("Isolation check requires Play Mode");
            }
        }

        if (GUILayout.Button("Log Isolation Status"))
        {
            if (Application.isPlaying)
            {
                var info = ItemRenderingLayerManager.GetIsolationInfo();
                Debug.Log($"Current isolation status: {info}");
            }
            else
            {
                Debug.Log("Isolation status requires Play Mode");
            }
        }

        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();

        if (GUILayout.Button("Log Texture Stats"))
        {
            if (Application.isPlaying && system != null)
            {
                RenderTextureManager textureManager = system.GetComponent<RenderTextureManager>();
                if (textureManager != null)
                {
                    textureManager.LogStats();
                }
                else
                {
                    Debug.Log("RenderTextureManager not found");
                }
            }
            else
            {
                Debug.Log("Texture stats require Play Mode");
            }
        }

        if (GUILayout.Button("Cleanup Texture Pools"))
        {
            if (Application.isPlaying && system != null)
            {
                RenderTextureManager textureManager = system.GetComponent<RenderTextureManager>();
                if (textureManager != null)
                {
                    textureManager.CleanupPools();
                    Debug.Log("Texture pools cleaned up");
                }
                else
                {
                    Debug.Log("RenderTextureManager not found");
                }
            }
            else
            {
                Debug.Log("Texture cleanup requires Play Mode");
            }
        }

        EditorGUILayout.EndHorizontal();

        if (GUILayout.Button("Open Item Rendering Window"))
        {
            ItemRenderingDebugWindow.ShowWindow();
        }

        EditorGUILayout.EndVertical();
    }
}
