using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

/// <summary>
/// Debug window for monitoring and configuring the item rendering system
/// </summary>
public class ItemRenderingDebugWindow : EditorWindow
{
    private Vector2 scrollPosition;
    private bool autoRefresh = true;
    private float refreshInterval = 1f;
    private double lastRefreshTime;

    // Tabs
    private int selectedTab = 0;
    private readonly string[] tabNames = { "Overview", "Cameras", "Textures", "Isolation", "Test" };

    // Test variables
    private ItemSO testItem;
    private int testTextureSize = 256;

    [MenuItem("Tools/Item Rendering/Debug Window")]
    public static void ShowWindow()
    {
        ItemRenderingDebugWindow window = GetWindow<ItemRenderingDebugWindow>("Item Rendering Debug");
        window.minSize = new Vector2(400, 300);
        window.Show();
    }

    void OnGUI()
    {
        DrawHeader();
        
        selectedTab = GUILayout.Toolbar(selectedTab, tabNames);
        
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
        
        switch (selectedTab)
        {
            case 0: DrawOverviewTab(); break;
            case 1: DrawCamerasTab(); break;
            case 2: DrawTexturesTab(); break;
            case 3: DrawIsolationTab(); break;
            case 4: DrawTestTab(); break;
        }
        
        EditorGUILayout.EndScrollView();
        
        DrawFooter();
        
        // Auto refresh
        if (autoRefresh && EditorApplication.timeSinceStartup - lastRefreshTime > refreshInterval)
        {
            Repaint();
            lastRefreshTime = EditorApplication.timeSinceStartup;
        }
    }

    void DrawHeader()
    {
        EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);
        
        GUILayout.Label("Item Rendering System Debug", EditorStyles.boldLabel);
        
        GUILayout.FlexibleSpace();
        
        autoRefresh = GUILayout.Toggle(autoRefresh, "Auto Refresh", EditorStyles.toolbarButton);
        
        if (GUILayout.Button("Refresh", EditorStyles.toolbarButton))
        {
            Repaint();
        }
        
        EditorGUILayout.EndHorizontal();
    }

    void DrawOverviewTab()
    {
        EditorGUILayout.LabelField("System Overview", EditorStyles.boldLabel);
        
        if (!Application.isPlaying)
        {
            EditorGUILayout.HelpBox("System information is only available in Play Mode", MessageType.Info);
            return;
        }

        // System status
        EditorGUILayout.BeginVertical(EditorStyles.helpBox);
        EditorGUILayout.LabelField("System Status", EditorStyles.boldLabel);
        
        bool systemExists = ItemRenderingSystem.Instance != null;
        EditorGUILayout.LabelField("System Instance", systemExists ? "✓ Active" : "✗ Not Found");
        
        if (systemExists)
        {
            var isolationInfo = ItemRenderingLayerManager.GetIsolationInfo();
            EditorGUILayout.LabelField("Isolation Status", isolationInfo.isFullyIsolated ? "✓ Fully Isolated" : "⚠ Partial");
            EditorGUILayout.LabelField("Render Layer", isolationInfo.itemRenderLayer.ToString());
        }
        
        EditorGUILayout.EndVertical();

        // Quick stats
        if (systemExists)
        {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("Quick Stats", EditorStyles.boldLabel);
            
            Camera[] allCameras = FindObjectsOfType<Camera>();
            ItemRenderCamera[] itemCameras = FindObjectsOfType<ItemRenderCamera>();
            
            EditorGUILayout.LabelField("Total Cameras", allCameras.Length.ToString());
            EditorGUILayout.LabelField("Item Render Cameras", itemCameras.Length.ToString());
            EditorGUILayout.LabelField("Main Game Cameras", (allCameras.Length - itemCameras.Length).ToString());
            
            EditorGUILayout.EndVertical();
        }
    }

    void DrawCamerasTab()
    {
        EditorGUILayout.LabelField("Camera Information", EditorStyles.boldLabel);
        
        Camera[] allCameras = FindObjectsOfType<Camera>();
        ItemRenderCamera[] itemCameras = FindObjectsOfType<ItemRenderCamera>();
        
        // Summary
        EditorGUILayout.BeginVertical(EditorStyles.helpBox);
        EditorGUILayout.LabelField($"Total Cameras: {allCameras.Length}");
        EditorGUILayout.LabelField($"Item Render Cameras: {itemCameras.Length}");
        EditorGUILayout.LabelField($"Main Game Cameras: {allCameras.Length - itemCameras.Length}");
        EditorGUILayout.EndVertical();
        
        // Item render cameras
        if (itemCameras.Length > 0)
        {
            EditorGUILayout.LabelField("Item Render Cameras", EditorStyles.boldLabel);
            foreach (var itemCam in itemCameras)
            {
                EditorGUILayout.BeginHorizontal(EditorStyles.helpBox);
                EditorGUILayout.LabelField(itemCam.name);
                EditorGUILayout.LabelField($"Layer: {itemCam.GetCamera().cullingMask}");
                if (GUILayout.Button("Select", GUILayout.Width(60)))
                {
                    Selection.activeGameObject = itemCam.gameObject;
                }
                EditorGUILayout.EndHorizontal();
            }
        }
        
        // Main cameras
        EditorGUILayout.LabelField("Main Game Cameras", EditorStyles.boldLabel);
        foreach (var cam in allCameras)
        {
            if (cam.GetComponent<ItemRenderCamera>() != null) continue;
            
            EditorGUILayout.BeginHorizontal(EditorStyles.helpBox);
            EditorGUILayout.LabelField(cam.name);
            
            if (Application.isPlaying)
            {
                var isolationInfo = ItemRenderingLayerManager.GetIsolationInfo();
                int layerMask = 1 << isolationInfo.itemRenderLayer;
                bool isIsolated = (cam.cullingMask & layerMask) == 0;
                EditorGUILayout.LabelField(isIsolated ? "✓ Isolated" : "⚠ Not Isolated");
            }
            
            if (GUILayout.Button("Select", GUILayout.Width(60)))
            {
                Selection.activeGameObject = cam.gameObject;
            }
            EditorGUILayout.EndHorizontal();
        }
    }

    void DrawTexturesTab()
    {
        EditorGUILayout.LabelField("Texture Information", EditorStyles.boldLabel);
        
        if (!Application.isPlaying)
        {
            EditorGUILayout.HelpBox("Texture information is only available in Play Mode", MessageType.Info);
            return;
        }

        if (ItemRenderingSystem.Instance != null)
        {
            RenderTextureManager textureManager = ItemRenderingSystem.Instance.GetComponent<RenderTextureManager>();
            if (textureManager != null)
            {
                var stats = textureManager.GetStats();
                
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);
                EditorGUILayout.LabelField("Texture Statistics", EditorStyles.boldLabel);
                EditorGUILayout.LabelField("Total Created", stats.totalCreated.ToString());
                EditorGUILayout.LabelField("Currently In Use", stats.totalInUse.ToString());
                EditorGUILayout.LabelField("Available in Pool", stats.totalPooled.ToString());
                EditorGUILayout.LabelField("Pool Count", stats.poolCount.ToString());
                EditorGUILayout.EndVertical();
                
                EditorGUILayout.Space();
                
                if (GUILayout.Button("Cleanup Pools"))
                {
                    textureManager.CleanupPools();
                }
                
                if (GUILayout.Button("Log Detailed Stats"))
                {
                    textureManager.LogStats();
                }
            }
            else
            {
                EditorGUILayout.HelpBox("RenderTextureManager not found", MessageType.Warning);
            }
        }
        else
        {
            EditorGUILayout.HelpBox("ItemRenderingSystem not found", MessageType.Warning);
        }
    }

    void DrawIsolationTab()
    {
        EditorGUILayout.LabelField("Layer Isolation", EditorStyles.boldLabel);
        
        if (Application.isPlaying)
        {
            var isolationInfo = ItemRenderingLayerManager.GetIsolationInfo();
            
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("Isolation Status", EditorStyles.boldLabel);
            EditorGUILayout.LabelField("Item Render Layer", isolationInfo.itemRenderLayer.ToString());
            EditorGUILayout.LabelField("Fully Isolated", isolationInfo.isFullyIsolated ? "✓ Yes" : "✗ No");
            EditorGUILayout.LabelField("Isolated Cameras", $"{isolationInfo.isolatedMainCameras}/{isolationInfo.totalMainCameras}");
            EditorGUILayout.LabelField("Item Render Cameras", isolationInfo.itemRenderCameras.ToString());
            EditorGUILayout.EndVertical();
            
            EditorGUILayout.Space();
            
            if (GUILayout.Button("Force Isolation Setup"))
            {
                ItemRenderingLayerManager.SetupIsolation();
            }
            
            if (GUILayout.Button("Restore All Cameras"))
            {
                ItemRenderingLayerManager.RestoreAllCameras();
            }
        }
        else
        {
            EditorGUILayout.HelpBox("Isolation information is only available in Play Mode", MessageType.Info);
        }
    }

    void DrawTestTab()
    {
        EditorGUILayout.LabelField("Test Item Rendering", EditorStyles.boldLabel);
        
        if (!Application.isPlaying)
        {
            EditorGUILayout.HelpBox("Testing is only available in Play Mode", MessageType.Info);
            return;
        }

        EditorGUILayout.BeginVertical(EditorStyles.helpBox);
        EditorGUILayout.LabelField("Test Configuration", EditorStyles.boldLabel);
        
        testItem = (ItemSO)EditorGUILayout.ObjectField("Test Item", testItem, typeof(ItemSO), false);
        testTextureSize = EditorGUILayout.IntSlider("Texture Size", testTextureSize, 64, 1024);
        
        EditorGUILayout.EndVertical();
        
        EditorGUILayout.Space();
        
        if (testItem != null && GUILayout.Button("Test Render Item"))
        {
            if (ItemRenderingSystem.Instance != null)
            {
                var request = ItemRenderingSystem.Instance.RequestItemRender(testItem, testTextureSize);
                if (request != null)
                {
                    Debug.Log($"Test render request created for {testItem.itemName}");
                }
                else
                {
                    Debug.LogError("Failed to create test render request");
                }
            }
            else
            {
                Debug.LogError("ItemRenderingSystem not found");
            }
        }
    }

    void DrawFooter()
    {
        EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);
        
        GUILayout.Label($"Last Update: {System.DateTime.Now:HH:mm:ss}");
        
        GUILayout.FlexibleSpace();
        
        refreshInterval = EditorGUILayout.Slider(refreshInterval, 0.5f, 5f, GUILayout.Width(100));
        GUILayout.Label("s", GUILayout.Width(15));
        
        EditorGUILayout.EndHorizontal();
    }
}
