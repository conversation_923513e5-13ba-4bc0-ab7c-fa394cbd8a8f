using UnityEngine;

/// <summary>
/// Handles individual item rendering with proper isolation, positioning, and render texture generation
/// </summary>
[RequireComponent(typeof(Camera))]
public class ItemRenderCamera : MonoBehaviour
{
    [Header("Camera Configuration")]
    [SerializeField] private Camera renderCamera;
    [SerializeField] private int renderLayer = 30;
    [SerializeField] private float cameraDistance = 2f;
    [SerializeField] private Color backgroundColor = Color.clear;

    [Header("Lighting")]
    [SerializeField] private Light itemLight;
    [SerializeField] private bool createDefaultLight = true;
    [SerializeField] private Color lightColor = Color.white;
    [SerializeField] private float lightIntensity = 1f;

    // Current rendering state
    private GameObject currentItem;
    private RenderTexture currentRenderTexture;
    private bool isInitialized = false;

    void Awake()
    {
        if (renderCamera == null)
        {
            renderCamera = GetComponent<Camera>();
        }
    }

    /// <summary>
    /// Initializes the camera with the specified settings
    /// </summary>
    public void Initialize(int layer, float fov, float distance)
    {
        renderLayer = layer;
        cameraDistance = distance;

        SetupCamera(fov);
        SetupLighting();
        
        isInitialized = true;
    }

    void SetupCamera()
    {
        SetupCamera(30f); // Default FOV
    }

    void SetupCamera(float fov)
    {
        if (renderCamera == null)
        {
            renderCamera = GetComponent<Camera>();
        }

        // Configure camera for item rendering
        renderCamera.clearFlags = CameraClearFlags.SolidColor;
        renderCamera.backgroundColor = backgroundColor;
        renderCamera.cullingMask = 1 << renderLayer; // Only render the item layer
        renderCamera.fieldOfView = fov;
        renderCamera.nearClipPlane = 0.1f;
        renderCamera.farClipPlane = 10f;
        renderCamera.orthographic = false;
        renderCamera.enabled = false; // We'll render manually

        // Ensure camera doesn't interfere with main rendering
        renderCamera.depth = -100; // Very low depth
        renderCamera.renderingPath = RenderingPath.Forward;
    }

    void SetupLighting()
    {
        if (createDefaultLight && itemLight == null)
        {
            // Create a dedicated light for item rendering
            GameObject lightGO = new GameObject("ItemLight");
            lightGO.transform.SetParent(transform);
            lightGO.transform.localPosition = Vector3.up * 2f + Vector3.forward * 2f;
            lightGO.transform.LookAt(transform.position);

            itemLight = lightGO.AddComponent<Light>();
            itemLight.type = LightType.Directional;
            itemLight.color = lightColor;
            itemLight.intensity = lightIntensity;
            itemLight.cullingMask = 1 << renderLayer; // Only light the item layer
            itemLight.shadows = LightShadows.None; // Disable shadows for performance
        }
    }

    /// <summary>
    /// Sets up the camera to render a specific item
    /// </summary>
    public void SetupForItem(GameObject item, ModelInfo modelInfo, RenderTexture renderTexture)
    {
        if (!isInitialized)
        {
            Debug.LogError("ItemRenderCamera not initialized. Call Initialize() first.");
            return;
        }

        currentItem = item;
        currentRenderTexture = renderTexture;

        // Position camera relative to item
        PositionCameraForItem(item, modelInfo);

        // Set render texture
        renderCamera.targetTexture = renderTexture;
    }

    void PositionCameraForItem(GameObject item, ModelInfo modelInfo)
    {
        if (item == null) return;

        // Calculate item bounds
        Bounds itemBounds = CalculateItemBounds(item);
        Vector3 itemCenter = itemBounds.center;
        
        // Apply model info center offset if available
        if (modelInfo != null)
        {
            itemCenter += modelInfo.centerOffset;
        }

        // Position camera to frame the item nicely
        Vector3 cameraDirection = Vector3.forward;
        if (modelInfo != null && modelInfo.intrinsicForward != Vector3.zero)
        {
            cameraDirection = -modelInfo.intrinsicForward.normalized;
        }

        // Calculate camera distance based on item size
        float itemSize = Mathf.Max(itemBounds.size.x, itemBounds.size.y, itemBounds.size.z);
        float adjustedDistance = Mathf.Max(cameraDistance, itemSize * 1.5f);

        // Position camera
        Vector3 cameraPosition = itemCenter + cameraDirection * adjustedDistance;
        transform.position = cameraPosition;
        transform.LookAt(itemCenter);

        // Apply default rotation if specified in model info
        if (modelInfo != null && modelInfo.defaultRotation != Vector3.zero)
        {
            item.transform.rotation = Quaternion.Euler(modelInfo.defaultRotation);
        }
    }

    Bounds CalculateItemBounds(GameObject item)
    {
        Renderer[] renderers = item.GetComponentsInChildren<Renderer>();
        if (renderers.Length == 0)
        {
            return new Bounds(item.transform.position, Vector3.one);
        }

        Bounds bounds = renderers[0].bounds;
        for (int i = 1; i < renderers.Length; i++)
        {
            bounds.Encapsulate(renderers[i].bounds);
        }

        return bounds;
    }

    /// <summary>
    /// Renders the current item to the render texture
    /// </summary>
    public void RenderItem()
    {
        if (currentRenderTexture == null)
        {
            Debug.LogError("No render texture assigned for item rendering");
            return;
        }

        if (currentItem == null)
        {
            Debug.LogError("No item assigned for rendering");
            return;
        }

        // Ensure the item is on the correct layer
        LayerUtility.SetLayerInChildren(currentItem, renderLayer);

        // Render the item
        renderCamera.Render();
    }

    /// <summary>
    /// Clears the current rendering setup
    /// </summary>
    public void ClearSetup()
    {
        currentItem = null;
        currentRenderTexture = null;
        renderCamera.targetTexture = null;
    }

    /// <summary>
    /// Gets the camera component
    /// </summary>
    public Camera GetCamera()
    {
        return renderCamera;
    }

    /// <summary>
    /// Checks if the camera is currently rendering an item
    /// </summary>
    public bool IsRenderingItem()
    {
        return currentItem != null && currentRenderTexture != null;
    }

    void OnDestroy()
    {
        // Clean up render texture assignment
        if (renderCamera != null)
        {
            renderCamera.targetTexture = null;
        }
    }

    void OnValidate()
    {
        // Update camera settings in editor
        if (renderCamera != null && Application.isPlaying)
        {
            SetupCamera();
        }
    }

#if UNITY_EDITOR
    void OnDrawGizmosSelected()
    {
        // Draw camera frustum and item bounds for debugging
        if (renderCamera != null)
        {
            Gizmos.color = Color.yellow;
            Gizmos.matrix = Matrix4x4.TRS(transform.position, transform.rotation, Vector3.one);
            Gizmos.DrawFrustum(Vector3.zero, renderCamera.fieldOfView, renderCamera.farClipPlane, renderCamera.nearClipPlane, renderCamera.aspect);
        }

        if (currentItem != null)
        {
            Gizmos.color = Color.green;
            Bounds bounds = CalculateItemBounds(currentItem);
            Gizmos.DrawWireCube(bounds.center, bounds.size);
        }
    }
#endif
}
