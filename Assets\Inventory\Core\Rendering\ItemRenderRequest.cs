using UnityEngine;

/// <summary>
/// Represents a request to render an item to a texture
/// </summary>
public class ItemRenderRequest
{
    public int requestId;
    public ItemSO item;
    public int textureSize;
    public RenderStatus status;
    public RenderTexture renderTexture;
    public ItemRenderCamera assignedCamera;
    public GameObject itemInstance;

    /// <summary>
    /// Gets the rendered texture if rendering is complete
    /// </summary>
    public RenderTexture GetTexture()
    {
        return status == RenderStatus.Complete ? renderTexture : null;
    }

    /// <summary>
    /// Checks if the render request is ready for use
    /// </summary>
    public bool IsReady()
    {
        return status == RenderStatus.Complete && renderTexture != null;
    }

    /// <summary>
    /// Checks if the render request has failed
    /// </summary>
    public bool HasFailed()
    {
        return status == RenderStatus.Failed;
    }

    /// <summary>
    /// Checks if the render request is still processing
    /// </summary>
    public bool IsProcessing()
    {
        return status == RenderStatus.Pending || status == RenderStatus.Rendering;
    }
}

/// <summary>
/// Status of a render request
/// </summary>
public enum RenderStatus
{
    Pending,    // Request is queued but not yet started
    Rendering,  // Request is currently being processed
    Complete,   // Rendering is finished and texture is ready
    Failed,     // Rendering failed for some reason
    Released    // Request has been released and resources cleaned up
}
