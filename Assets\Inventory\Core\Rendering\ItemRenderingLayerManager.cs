using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Manages layer isolation for the item rendering system
/// Ensures complete separation between item rendering and main game cameras
/// </summary>
public static class ItemRenderingLayerManager
{
    // Default layer for item rendering (can be configured)
    public const int DEFAULT_ITEM_RENDER_LAYER = 30;
    
    // Layer name for easy identification
    public const string ITEM_RENDER_LAYER_NAME = "ItemRender";
    
    // Cache for main game cameras to restore their culling masks
    private static Dictionary<Camera, int> originalCullingMasks = new Dictionary<Camera, int>();
    
    // Track if isolation has been set up
    private static bool isIsolationSetup = false;
    
    /// <summary>
    /// Sets up complete isolation for item rendering
    /// </summary>
    public static void SetupIsolation(int itemRenderLayer = DEFAULT_ITEM_RENDER_LAYER)
    {
        if (isIsolationSetup) return;
        
        Debug.Log($"Setting up item rendering isolation on layer {itemRenderLayer}");
        
        // Ensure the layer exists and is named correctly
        SetupRenderLayer(itemRenderLayer);
        
        // Isolate all existing cameras from the item render layer
        IsolateExistingCameras(itemRenderLayer);
        
        // Set up automatic isolation for new cameras
        SetupAutomaticIsolation(itemRenderLayer);
        
        isIsolationSetup = true;
    }
    
    /// <summary>
    /// Sets up the render layer with proper naming
    /// </summary>
    static void SetupRenderLayer(int layerIndex)
    {
        if (layerIndex < 0 || layerIndex > 31)
        {
            Debug.LogError($"Invalid layer index {layerIndex}. Must be between 0 and 31.");
            return;
        }
        
#if UNITY_EDITOR
        // In editor, we can set the layer name
        string currentLayerName = UnityEditor.TagManager.GetLayerName(layerIndex);
        if (string.IsNullOrEmpty(currentLayerName) || currentLayerName != ITEM_RENDER_LAYER_NAME)
        {
            Debug.Log($"Setting layer {layerIndex} name to '{ITEM_RENDER_LAYER_NAME}'");
            // Note: Setting layer names requires TagManager manipulation which is complex
            // For now, we'll just log the recommendation
            Debug.Log($"Please manually set layer {layerIndex} name to '{ITEM_RENDER_LAYER_NAME}' in Project Settings > Tags and Layers");
        }
#endif
    }
    
    /// <summary>
    /// Isolates all existing cameras from the item render layer
    /// </summary>
    static void IsolateExistingCameras(int itemRenderLayer)
    {
        Camera[] allCameras = Object.FindObjectsOfType<Camera>();
        int layerMask = 1 << itemRenderLayer;
        
        foreach (Camera cam in allCameras)
        {
            // Skip if this is already an item render camera
            if (cam.GetComponent<ItemRenderCamera>() != null)
                continue;
                
            // Store original culling mask
            if (!originalCullingMasks.ContainsKey(cam))
            {
                originalCullingMasks[cam] = cam.cullingMask;
            }
            
            // Remove item render layer from culling mask
            if ((cam.cullingMask & layerMask) != 0)
            {
                cam.cullingMask &= ~layerMask;
                Debug.Log($"Isolated camera '{cam.name}' from item render layer {itemRenderLayer}");
            }
        }
    }
    
    /// <summary>
    /// Sets up automatic isolation for cameras created at runtime
    /// </summary>
    static void SetupAutomaticIsolation(int itemRenderLayer)
    {
        // Create a monitoring component that will handle new cameras
        GameObject monitor = new GameObject("ItemRenderingIsolationMonitor");
        monitor.AddComponent<ItemRenderingIsolationMonitor>().Initialize(itemRenderLayer);
        Object.DontDestroyOnLoad(monitor);
    }
    
    /// <summary>
    /// Manually isolates a specific camera from the item render layer
    /// </summary>
    public static void IsolateCamera(Camera camera, int itemRenderLayer = DEFAULT_ITEM_RENDER_LAYER)
    {
        if (camera == null) return;
        
        // Skip if this is an item render camera
        if (camera.GetComponent<ItemRenderCamera>() != null) return;
        
        int layerMask = 1 << itemRenderLayer;
        
        // Store original culling mask if not already stored
        if (!originalCullingMasks.ContainsKey(camera))
        {
            originalCullingMasks[camera] = camera.cullingMask;
        }
        
        // Remove item render layer from culling mask
        if ((camera.cullingMask & layerMask) != 0)
        {
            camera.cullingMask &= ~layerMask;
            Debug.Log($"Isolated camera '{camera.name}' from item render layer {itemRenderLayer}");
        }
    }
    
    /// <summary>
    /// Restores a camera's original culling mask
    /// </summary>
    public static void RestoreCamera(Camera camera)
    {
        if (camera == null) return;
        
        if (originalCullingMasks.TryGetValue(camera, out int originalMask))
        {
            camera.cullingMask = originalMask;
            originalCullingMasks.Remove(camera);
            Debug.Log($"Restored camera '{camera.name}' original culling mask");
        }
    }
    
    /// <summary>
    /// Restores all cameras to their original culling masks
    /// </summary>
    public static void RestoreAllCameras()
    {
        foreach (var kvp in originalCullingMasks)
        {
            if (kvp.Key != null)
            {
                kvp.Key.cullingMask = kvp.Value;
            }
        }
        originalCullingMasks.Clear();
        isIsolationSetup = false;
        Debug.Log("Restored all cameras to original culling masks");
    }
    
    /// <summary>
    /// Checks if a layer is isolated from main cameras
    /// </summary>
    public static bool IsLayerIsolated(int layerIndex)
    {
        Camera[] allCameras = Object.FindObjectsOfType<Camera>();
        int layerMask = 1 << layerIndex;
        
        foreach (Camera cam in allCameras)
        {
            // Skip item render cameras
            if (cam.GetComponent<ItemRenderCamera>() != null)
                continue;
                
            // If any main camera can see this layer, it's not isolated
            if ((cam.cullingMask & layerMask) != 0)
                return false;
        }
        
        return true;
    }
    
    /// <summary>
    /// Gets information about layer isolation status
    /// </summary>
    public static LayerIsolationInfo GetIsolationInfo(int itemRenderLayer = DEFAULT_ITEM_RENDER_LAYER)
    {
        Camera[] allCameras = Object.FindObjectsOfType<Camera>();
        ItemRenderCamera[] itemCameras = Object.FindObjectsOfType<ItemRenderCamera>();
        
        int mainCamerasCount = allCameras.Length - itemCameras.Length;
        int isolatedCamerasCount = 0;
        int layerMask = 1 << itemRenderLayer;
        
        foreach (Camera cam in allCameras)
        {
            if (cam.GetComponent<ItemRenderCamera>() != null)
                continue;
                
            if ((cam.cullingMask & layerMask) == 0)
                isolatedCamerasCount++;
        }
        
        return new LayerIsolationInfo
        {
            itemRenderLayer = itemRenderLayer,
            totalMainCameras = mainCamerasCount,
            isolatedMainCameras = isolatedCamerasCount,
            itemRenderCameras = itemCameras.Length,
            isFullyIsolated = isolatedCamerasCount == mainCamerasCount
        };
    }
}

/// <summary>
/// Information about layer isolation status
/// </summary>
[System.Serializable]
public struct LayerIsolationInfo
{
    public int itemRenderLayer;
    public int totalMainCameras;
    public int isolatedMainCameras;
    public int itemRenderCameras;
    public bool isFullyIsolated;
    
    public override string ToString()
    {
        return $"Layer {itemRenderLayer}: {isolatedMainCameras}/{totalMainCameras} main cameras isolated, {itemRenderCameras} item cameras, Fully Isolated: {isFullyIsolated}";
    }
}
